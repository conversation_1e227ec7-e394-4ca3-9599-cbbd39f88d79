# 上海市算力调度平台 API 文档

## 📋 目录

- [1. 概述](#1-概述)
- [2. 快速开始](#2-快速开始)
  - [2.1 环境准备](#21-环境准备)
  - [2.2 获取访问凭证](#22-获取访问凭证)
  - [2.3 请求签名](#23-请求签名)
- [3. API 接口](#3-api-接口)
  - [3.1 算力产品管理](#31-算力产品管理)
  - [3.2 集群资源统计](#32-集群资源统计)
- [4. 错误处理](#4-错误处理)
- [5. 示例代码](#5-示例代码)

---

## 1. 概述

### 1.1 平台简介

上海市算力调度平台提供统一的算力资源调度和管理服务，通过 OpenAPI 接口，算力提供方可以：

- 实时更新算力产品信息（价格、库存等）
- 上报数据中心集群的资源使用情况
- 获取算力调度相关的统计数据

### 1.2 技术规范

- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **认证方式**: MTLS双向证书 + AK/SK签名
- **签名算法**: HMAC-SHA256
- **服务器地址**: `http://openapi.taopower.ai`

### 1.3 安全要求

本系统采用双重安全机制：
1. **MTLS证书认证**: 需要安装平台颁发的客户端证书
2. **AK/SK签名**: 每个请求都需要携带签名验证身份

---

## 2. 快速开始

### 2.1 环境准备

#### MTLS证书配置

由于安全合规要求，所有API调用都需要使用MTLS双向证书认证：

1. **申请证书**: 向平台提供出口IP列表，获取授权证书
2. **安装证书**: 
   - 前端访问：在浏览器中安装证书
   - API调用：在HTTP客户端库中配置证书

### 2.2 获取访问凭证

#### 什么是 AK/SK？

- **AK (Access Key)**: 访问密钥，相当于用户名
- **SK (Secret Key)**: 私密密钥，相当于密码

#### 获取步骤

1. 登录 [算力调度平台](https://shanghaiai.com/)
2. 点击用户头像 → 选择 "Access key"
3. 点击 "生成" 按钮创建新的 AK/SK 对
4. 复制生成的 AK/SK 用于后续API调用

⚠️ **重要提醒**: SK只显示一次，请妥善保存

### 2.3 请求签名

#### 签名机制

所有API请求都必须携带以下Header参数：

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `ts` | integer | 请求时间戳（毫秒） | 1723448959001 |
| `accessKey` | string | 访问密钥 | your-access-key |
| `sign` | string | 请求签名 | calculated-signature |

#### 签名算法

**步骤1**: 构造查询参数字符串
```
如果URL包含查询参数，按参数名ASCII字典序排序并拼接
例如: ?d=1&b=2&a=3&c=4 → a3b2c4d1
```

**步骤2**: 构造待签名字符串
```
待签名字符串 = 时间戳 + AccessKey + 查询参数 + 请求体
```

**步骤3**: 生成签名
```
sign = HMAC-SHA256(SecretKey, 待签名字符串)
```

#### 签名示例

假设有以下请求：
```http
POST /api/v1/example?d=1&b=2&a=3&c=4
Content-Type: application/json
{"dummy": "val"}
```

**计算过程**:
1. 查询参数排序: `a3b2c4d1`
2. 待签名字符串: `1723448959001your-access-keya3b2c4d1{"dummy": "val"}`
3. 使用SK通过HMAC-SHA256计算签名

---

## 3. API 接口

### 3.1 算力产品管理

#### 更新产品实时数据

**接口描述**: 算力接入方使用此接口更新产品的价格、库存等实时数据

**请求信息**:
- **方法**: `POST`
- **路径**: `/powerapi/v1/devices`
- **优先级**: P1（高优先级）

**请求参数**:

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| devices | array | 是 | 算力产品对象数组 |

**Device对象结构**:

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | string | 是 | 审核通过的算力产品ID |
| price | integer | 否 | 产品价格 |
| price_unit | integer | 否 | 计价单位：1=时，2=天，3=月 |
| device_number | integer | 否 | 库存数量 |

**请求示例**:
```json
{
    "devices": [
        {
            "id": "12345",
            "price": 100,
            "price_unit": 2,
            "device_number": 50
        },
        {
            "id": "12346", 
            "price": 200,
            "price_unit": 3,
            "device_number": 30
        }
    ]
}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "All products processed successfully."
}
```

**调用建议**:
- 可定时调用（建议10秒间隔）或数据变化时调用
- 调用失败时需重试直到成功

### 3.2 集群资源统计

#### 数据中心集群算力统计

**接口描述**: 更新数据中心集群的算力资源使用情况，包括网络状况、总算力、已用算力和空闲算力

**请求信息**:
- **方法**: `POST`
- **路径**: `/powerapi/v1/clusters`
- **优先级**: P0（最高优先级）

**请求参数**:

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| clusters | array | 是 | 数据中心集群对象数组 |

**Cluster对象结构**:

| 字段 | 类型 | 必填 | 说明 | 单位 |
|------|------|------|------|------|
| id | string | 是 | 数据中心集群ID | - |
| ping | integer | 是 | 网络延迟 | ms |
| total_flops | integer | 是 | GPU总算力 | TFLOPS(f32) |
| used_flops | integer | 是 | GPU已用算力 | TFLOPS(f32) |
| available_flops | integer | 是 | GPU空闲算力 | TFLOPS(f32) |
| total_cpu_cores | integer | 是 | CPU总核数 | 核 |
| used_cpu_cores | integer | 是 | CPU已用核数 | 核 |
| available_cpu_cores | integer | 是 | CPU空闲核数 | 核 |
| total_mem_size | integer | 是 | 总内存 | GB |
| used_mem_size | integer | 是 | 已用内存 | GB |
| available_mem_size | integer | 是 | 空闲内存 | GB |
| total_gpu_cards | integer | 是 | GPU总卡数 | 张 |
| used_gpu_cards | integer | 是 | GPU已用卡数 | 张 |
| available_gpu_cards | integer | 是 | GPU空闲卡数 | 张 |
| total_gpu_mem_size | integer | 是 | GPU总显存 | GB |
| used_gpu_mem_size | integer | 是 | GPU已用显存 | GB |
| available_gpu_mem_size | integer | 是 | GPU空闲显存 | GB |
| total_storage_size | integer | 是 | 总存储容量 | GB |
| used_storage_size | integer | 是 | 已用存储容量 | GB |
| available_storage_size | integer | 是 | 空闲存储容量 | GB |

**请求示例**:
```json
{
    "clusters": [
        {
            "id": "23456",
            "ping": 10,
            "total_flops": 29600,
            "used_flops": 0,
            "available_flops": 29600,
            "total_cpu_cores": 2038,
            "used_cpu_cores": 0,
            "available_cpu_cores": 2038,
            "total_mem_size": 12000,
            "used_mem_size": 0,
            "available_mem_size": 12000,
            "total_gpu_cards": 80,
            "used_gpu_cards": 0,
            "available_gpu_cards": 80,
            "total_gpu_mem_size": 6000,
            "used_gpu_mem_size": 0,
            "available_gpu_mem_size": 6000,
            "total_storage_size": 50000,
            "used_storage_size": 10000,
            "available_storage_size": 40000
        }
    ]
}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "succ"
}
```

**调用建议**:
- 可定时调用（建议10秒间隔）或数据变化时调用
- 调用失败时需重试直到成功

---

## 4. 错误处理

### 4.1 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 401 | 认证失败 | 检查AK/SK是否正确，签名是否有效 |
| 403 | 权限不足 | 确认证书是否正确安装，IP是否在白名单 |
| 400 | 请求参数错误 | 检查请求格式和必填参数 |
| 500 | 服务器内部错误 | 稍后重试或联系技术支持 |

### 4.2 签名验证失败

**常见原因**:
1. 时间戳过期（超过5分钟）
2. 查询参数排序错误
3. 请求体格式不正确
4. SK密钥错误

**调试建议**:
1. 确认系统时间准确
2. 验证待签名字符串构造是否正确
3. 使用在线工具验证HMAC-SHA256计算结果

---

## 5. 示例代码

### 5.1 Python 示例

```python
import hmac
import hashlib
import time
import json
import requests
from urllib.parse import urlencode

class TaoPowerAPI:
    def __init__(self, access_key, secret_key, base_url="http://openapi.taopower.ai"):
        self.access_key = access_key
        self.secret_key = secret_key
        self.base_url = base_url

    def _generate_signature(self, method, path, query_params=None, body=None):
        # 生成时间戳
        ts = int(time.time() * 1000)

        # 处理查询参数
        qs = ""
        if query_params:
            sorted_params = sorted(query_params.items())
            qs = "".join([f"{k}{v}" for k, v in sorted_params])

        # 处理请求体
        body_str = ""
        if body:
            body_str = json.dumps(body, separators=(',', ':'))

        # 构造待签名字符串
        to_sign = f"{ts}{self.access_key}{qs}{body_str}"

        # 生成签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            to_sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return ts, signature

    def update_devices(self, devices):
        """更新算力产品数据"""
        path = "/powerapi/v1/devices"
        body = {"devices": devices}

        ts, signature = self._generate_signature("POST", path, body=body)

        headers = {
            "Content-Type": "application/json",
            "ts": str(ts),
            "accessKey": self.access_key,
            "sign": signature
        }

        response = requests.post(
            f"{self.base_url}{path}",
            headers=headers,
            json=body,
            verify=False  # 注意：生产环境需要配置证书验证
        )

        return response.json()

    def update_clusters(self, clusters):
        """更新集群资源统计"""
        path = "/powerapi/v1/clusters"
        body = {"clusters": clusters}

        ts, signature = self._generate_signature("POST", path, body=body)

        headers = {
            "Content-Type": "application/json",
            "ts": str(ts),
            "accessKey": self.access_key,
            "sign": signature
        }

        response = requests.post(
            f"{self.base_url}{path}",
            headers=headers,
            json=body,
            verify=False  # 注意：生产环境需要配置证书验证
        )

        return response.json()

# 使用示例
if __name__ == "__main__":
    # 初始化API客户端
    api = TaoPowerAPI("your-access-key", "your-secret-key")

    # 更新设备信息
    devices = [
        {
            "id": "12345",
            "price": 100,
            "price_unit": 2,
            "device_number": 50
        }
    ]
    result = api.update_devices(devices)
    print("设备更新结果:", result)

    # 更新集群信息
    clusters = [
        {
            "id": "23456",
            "ping": 10,
            "total_flops": 29600,
            "used_flops": 0,
            "available_flops": 29600,
            # ... 其他字段
        }
    ]
    result = api.update_clusters(clusters)
    print("集群更新结果:", result)
```

### 5.2 Go 示例

```go
package main

import (
    "bytes"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "sort"
    "strconv"
    "strings"
    "time"
)

type TaoPowerAPI struct {
    AccessKey string
    SecretKey string
    BaseURL   string
}

type DeviceItem struct {
    ID           string `json:"id"`
    Price        int    `json:"price"`
    PriceUnit    int    `json:"price_unit"`
    DeviceNumber int    `json:"device_number"`
}

type DeviceRequest struct {
    Devices []DeviceItem `json:"devices"`
}

func NewTaoPowerAPI(accessKey, secretKey string) *TaoPowerAPI {
    return &TaoPowerAPI{
        AccessKey: accessKey,
        SecretKey: secretKey,
        BaseURL:   "http://openapi.taopower.ai",
    }
}

func (api *TaoPowerAPI) generateSignature(method, path string, queryParams map[string]string, body []byte) (int64, string) {
    // 生成时间戳
    ts := time.Now().UnixMilli()

    // 处理查询参数
    var qs string
    if len(queryParams) > 0 {
        var keys []string
        for k := range queryParams {
            keys = append(keys, k)
        }
        sort.Strings(keys)

        var parts []string
        for _, k := range keys {
            parts = append(parts, k+queryParams[k])
        }
        qs = strings.Join(parts, "")
    }

    // 构造待签名字符串
    toSign := strconv.FormatInt(ts, 10) + api.AccessKey + qs + string(body)

    // 生成签名
    h := hmac.New(sha256.New, []byte(api.SecretKey))
    h.Write([]byte(toSign))
    signature := hex.EncodeToString(h.Sum(nil))

    return ts, signature
}

func (api *TaoPowerAPI) UpdateDevices(devices []DeviceItem) (map[string]interface{}, error) {
    path := "/powerapi/v1/devices"

    reqBody := DeviceRequest{Devices: devices}
    bodyBytes, err := json.Marshal(reqBody)
    if err != nil {
        return nil, err
    }

    ts, signature := api.generateSignature("POST", path, nil, bodyBytes)

    req, err := http.NewRequest("POST", api.BaseURL+path, bytes.NewBuffer(bodyBytes))
    if err != nil {
        return nil, err
    }

    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("ts", strconv.FormatInt(ts, 10))
    req.Header.Set("accessKey", api.AccessKey)
    req.Header.Set("sign", signature)

    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    respBody, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, err
    }

    var result map[string]interface{}
    err = json.Unmarshal(respBody, &result)
    return result, err
}

func main() {
    // 初始化API客户端
    api := NewTaoPowerAPI("your-access-key", "your-secret-key")

    // 更新设备信息
    devices := []DeviceItem{
        {
            ID:           "12345",
            Price:        100,
            PriceUnit:    2,
            DeviceNumber: 50,
        },
    }

    result, err := api.UpdateDevices(devices)
    if err != nil {
        fmt.Printf("错误: %v\n", err)
        return
    }

    fmt.Printf("设备更新结果: %+v\n", result)
}
```

### 5.3 Java 示例

```java
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Hex;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;

public class TaoPowerAPI {
    private final String accessKey;
    private final String secretKey;
    private final String baseUrl;
    private final OkHttpClient client;
    private final ObjectMapper objectMapper;

    public TaoPowerAPI(String accessKey, String secretKey) {
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.baseUrl = "http://openapi.taopower.ai";
        this.client = new OkHttpClient();
        this.objectMapper = new ObjectMapper();
    }

    private String[] generateSignature(String method, String path, Map<String, String> queryParams, String body)
            throws NoSuchAlgorithmException, InvalidKeyException {
        // 生成时间戳
        long ts = System.currentTimeMillis();

        // 处理查询参数
        StringBuilder qs = new StringBuilder();
        if (queryParams != null && !queryParams.isEmpty()) {
            List<String> keys = new ArrayList<>(queryParams.keySet());
            Collections.sort(keys);
            for (String key : keys) {
                qs.append(key).append(queryParams.get(key));
            }
        }

        // 构造待签名字符串
        String toSign = ts + accessKey + qs.toString() + (body != null ? body : "");

        // 生成签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signatureBytes = mac.doFinal(toSign.getBytes(StandardCharsets.UTF_8));
        String signature = Hex.encodeHexString(signatureBytes);

        return new String[]{String.valueOf(ts), signature};
    }

    public Map<String, Object> updateDevices(List<DeviceItem> devices) throws Exception {
        String path = "/powerapi/v1/devices";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("devices", devices);
        String bodyJson = objectMapper.writeValueAsString(requestBody);

        String[] signatureData = generateSignature("POST", path, null, bodyJson);

        RequestBody body = RequestBody.create(bodyJson, MediaType.get("application/json"));
        Request request = new Request.Builder()
                .url(baseUrl + path)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("ts", signatureData[0])
                .addHeader("accessKey", accessKey)
                .addHeader("sign", signatureData[1])
                .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, Map.class);
        }
    }

    public static class DeviceItem {
        public String id;
        public int price;
        public int price_unit;
        public int device_number;

        public DeviceItem(String id, int price, int priceUnit, int deviceNumber) {
            this.id = id;
            this.price = price;
            this.price_unit = priceUnit;
            this.device_number = deviceNumber;
        }
    }

    public static void main(String[] args) {
        try {
            // 初始化API客户端
            TaoPowerAPI api = new TaoPowerAPI("your-access-key", "your-secret-key");

            // 更新设备信息
            List<DeviceItem> devices = Arrays.asList(
                new DeviceItem("12345", 100, 2, 50)
            );

            Map<String, Object> result = api.updateDevices(devices);
            System.out.println("设备更新结果: " + result);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

---

## 6. 最佳实践

### 6.1 性能优化

1. **批量更新**: 尽量将多个设备或集群的更新合并到一个请求中
2. **合理频率**: 建议更新频率不超过每10秒一次
3. **连接复用**: 使用HTTP连接池减少连接开销

### 6.2 错误处理

1. **重试机制**: 对于网络错误和5xx错误实施指数退避重试
2. **日志记录**: 记录所有API调用的请求和响应用于问题排查
3. **监控告警**: 设置API调用成功率和延迟监控

### 6.3 安全建议

1. **密钥管理**: SK密钥应存储在安全的配置管理系统中
2. **网络安全**: 生产环境必须使用HTTPS和有效的证书验证
3. **访问控制**: 定期轮换AK/SK，及时撤销不需要的访问权限

---

## 7. 联系支持

- **技术文档**: [http://aioc.taopower.ai](http://aioc.taopower.ai)
- **平台首页**: [http://www.taopower.ai](http://www.taopower.ai)
- **问题反馈**: 通过平台工单系统提交技术支持请求

---

*本文档基于 openapi 2025-03-19.md 生成，保持了所有技术规范的准确性，并增强了可读性和实用性。*

